{"version": 3, "file": "scram.js", "sourceRoot": "", "sources": ["../../../src/cmap/auth/scram.ts"], "names": [], "mappings": ";;;AAAA,mDAAgD;AAChD,iCAAiC;AACjC,+BAAiC;AAEjC,qCAAmD;AACnD,uCAIqB;AACrB,uCAAiC;AAEjC,mDAAiE;AAEjE,2CAA4C;AAI5C,MAAM,QAAS,SAAQ,4BAAY;IAGjC,YAAY,YAA0B;QACpC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,MAAM,CAAC;QAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAA,gBAAS,EAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACxD,CAAC;IAEQ,KAAK,CAAC,OAAO,CACpB,YAA+B,EAC/B,WAAwB;QAExB,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAC5C,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,oCAA4B,CAAC,uCAAuC,CAAC,CAAC;SACjF;QAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAC9C,gCAAgC;QAChC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;QAE1B,MAAM,OAAO,GAAG;YACd,GAAG,YAAY;YACf,uBAAuB,EAAE;gBACvB,GAAG,gBAAgB,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,CAAC;gBACrD,EAAE,EAAE,WAAW,CAAC,MAAM;aACvB;SACF,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAEQ,KAAK,CAAC,IAAI,CAAC,WAAwB;QAC1C,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC;QACnD,IAAI,QAAQ,EAAE,uBAAuB,IAAI,CAAC,gBAAgB,EAAE;YAC1D,OAAO,yBAAyB,CAC9B,IAAI,CAAC,YAAY,EACjB,QAAQ,CAAC,uBAAuB,EAChC,WAAW,CACZ,CAAC;SACH;QACD,OAAO,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;IACtD,CAAC;CACF;AAED,SAAS,aAAa,CAAC,QAAgB;IACrC,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC1D,CAAC;AAED,SAAS,sBAAsB,CAAC,QAAgB,EAAE,KAAa;IAC7D,qFAAqF;IACrF,kEAAkE;IAClE,OAAO,MAAM,CAAC,MAAM,CAAC;QACnB,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC;QAC7B,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC;QAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;KAC9C,CAAC,CAAC;AACL,CAAC;AAED,SAAS,gBAAgB,CACvB,YAA0B,EAC1B,WAA6B,EAC7B,KAAa;IAEb,MAAM,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACrD,MAAM,SAAS,GACb,YAAY,KAAK,MAAM,CAAC,CAAC,CAAC,yBAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,yBAAa,CAAC,oBAAoB,CAAC;IAElG,qFAAqF;IACrF,kEAAkE;IAClE,OAAO;QACL,SAAS,EAAE,CAAC;QACZ,SAAS;QACT,OAAO,EAAE,IAAI,aAAM,CACjB,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CACrF;QACD,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE;KACrC,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,YAAY,CAAC,YAA0B,EAAE,WAAwB;IAC9E,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;IAChD,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,oCAA4B,CAAC,uCAAuC,CAAC,CAAC;KACjF;IACD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;QACtB,MAAM,IAAI,iCAAyB,CAAC,iDAAiD,CAAC,CAAC;KACxF;IACD,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IAChC,MAAM,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC;IAE9B,MAAM,YAAY,GAAG,gBAAgB,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IACxE,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,IAAA,UAAE,EAAC,GAAG,EAAE,OAAO,CAAC,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;IAC1F,MAAM,yBAAyB,CAAC,YAAY,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;AACvE,CAAC;AAED,KAAK,UAAU,yBAAyB,CACtC,YAA0B,EAC1B,QAAkB,EAClB,WAAwB;IAExB,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;IAC1C,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;IAC5C,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,oCAA4B,CAAC,uCAAuC,CAAC,CAAC;KACjF;IACD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;QACtB,MAAM,IAAI,iCAAyB,CAAC,8CAA8C,CAAC,CAAC;KACrF;IACD,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IAEhC,MAAM,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC;IAC9B,MAAM,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACrD,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;IAEtC,MAAM,iBAAiB,GACrB,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAA,mBAAQ,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEtF,MAAM,OAAO,GAAW,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;QACvD,CAAC,CAAC,IAAI,aAAM,CAAC,QAAQ,CAAC,OAAO,CAAC;QAC9B,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC;IAErB,MAAM,IAAI,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IAEnC,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACxC,IAAI,UAAU,IAAI,UAAU,GAAG,IAAI,EAAE;QACnC,kBAAkB;QAClB,MAAM,IAAI,yBAAiB,CAAC,8CAA8C,UAAU,EAAE,CAAC,CAAC;KACzF;IAED,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;IACpB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;IACtB,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;QAC9B,kBAAkB;QAClB,MAAM,IAAI,yBAAiB,CAAC,qCAAqC,MAAM,EAAE,CAAC,CAAC;KAC5E;IAED,wBAAwB;IACxB,MAAM,YAAY,GAAG,YAAY,MAAM,EAAE,CAAC;IAC1C,MAAM,cAAc,GAAG,EAAE,CACvB,iBAAiB,EACjB,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAC3B,UAAU,EACV,YAAY,CACb,CAAC;IAEF,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;IACnE,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;IACnE,MAAM,SAAS,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IAC7C,MAAM,WAAW,GAAG;QAClB,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC;QACvC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;QACxB,YAAY;KACb,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEZ,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IACnE,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE,CAAC;IAC3D,MAAM,WAAW,GAAG,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE1D,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IACnE,MAAM,eAAe,GAAG;QACtB,YAAY,EAAE,CAAC;QACf,cAAc,EAAE,QAAQ,CAAC,cAAc;QACvC,OAAO,EAAE,IAAI,aAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAC9C,CAAC;IAEF,MAAM,CAAC,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,IAAA,UAAE,EAAC,GAAG,EAAE,OAAO,CAAC,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC;IACtF,MAAM,cAAc,GAAG,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAE/C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,eAAe,CAAC,EAAE;QAC5E,MAAM,IAAI,yBAAiB,CAAC,sCAAsC,CAAC,CAAC;KACrE;IAED,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE;QACpB,0DAA0D;QAC1D,OAAO;KACR;IAED,MAAM,oBAAoB,GAAG;QAC3B,YAAY,EAAE,CAAC;QACf,cAAc,EAAE,CAAC,CAAC,cAAc;QAChC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;KACzB,CAAC;IAEF,MAAM,UAAU,CAAC,YAAY,CAAC,IAAA,UAAE,EAAC,GAAG,EAAE,OAAO,CAAC,EAAE,oBAAoB,EAAE,SAAS,CAAC,CAAC;AACnF,CAAC;AAED,SAAS,YAAY,CAAC,OAAe;IACnC,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC5C,MAAM,IAAI,GAAa,EAAE,CAAC;IAC1B,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;KACrC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,cAAc,CAAC,QAAgB,EAAE,QAAgB;IACxD,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAChC,MAAM,IAAI,iCAAyB,CAAC,2BAA2B,CAAC,CAAC;KAClE;IAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAChC,MAAM,IAAI,iCAAyB,CAAC,2BAA2B,CAAC,CAAC;KAClE;IAED,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;QACzB,MAAM,IAAI,iCAAyB,CAAC,0BAA0B,CAAC,CAAC;KACjE;IAED,IAAI,GAAgB,CAAC;IACrB,IAAI;QACF,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAChC;IAAC,OAAO,GAAG,EAAE;QACZ,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE;YACpB,oFAAoF;YACpF,wFAAwF;YACxF,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;SAC7E;QACD,MAAM,GAAG,CAAC;KACX;IACD,GAAG,CAAC,MAAM,CAAC,GAAG,QAAQ,UAAU,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;IACpD,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED,kBAAkB;AAClB,SAAS,GAAG,CAAC,CAAS,EAAE,CAAS;IAC/B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACpB;IAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACpB;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;IAC5C,MAAM,GAAG,GAAG,EAAE,CAAC;IAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAClC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvB;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,CAAC,CAAC,MAAoB,EAAE,IAAY;IAC3C,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;AACzD,CAAC;AAED,SAAS,IAAI,CAAC,MAAoB,EAAE,GAAW,EAAE,IAAqB;IACpE,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;AAC9D,CAAC;AAMD,IAAI,QAAQ,GAAY,EAAE,CAAC;AAC3B,IAAI,aAAa,GAAG,CAAC,CAAC;AACtB,SAAS,aAAa;IACpB,QAAQ,GAAG,EAAE,CAAC;IACd,aAAa,GAAG,CAAC,CAAC;AACpB,CAAC;AAED,MAAM,WAAW,GAAG;IAClB,MAAM,EAAE,EAAE;IACV,IAAI,EAAE,EAAE;CACT,CAAC;AAEF,SAAS,EAAE,CAAC,IAAY,EAAE,IAAY,EAAE,UAAkB,EAAE,YAA0B;IACpF,qCAAqC;IACrC,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClE,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;QACzB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;KACtB;IAED,oBAAoB;IACpB,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAClC,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,WAAW,CAAC,YAAY,CAAC,EACzB,YAAY,CACb,CAAC;IAEF,+EAA+E;IAC/E,IAAI,aAAa,IAAI,GAAG,EAAE;QACxB,aAAa,EAAE,CAAC;KACjB;IAED,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;IAC3B,aAAa,IAAI,CAAC,CAAC;IACnB,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,aAAa,CAAC,GAAW,EAAE,GAAe;IACjD,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE;QAC7B,OAAO,KAAK,CAAC;KACd;IAED,IAAI,OAAO,MAAM,CAAC,eAAe,KAAK,UAAU,EAAE;QAChD,OAAO,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;KACzC;IAED,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3B;IAED,OAAO,MAAM,KAAK,CAAC,CAAC;AACtB,CAAC;AAED,MAAa,SAAU,SAAQ,QAAQ;IACrC;QACE,KAAK,CAAC,MAAM,CAAC,CAAC;IAChB,CAAC;CACF;AAJD,8BAIC;AAED,MAAa,WAAY,SAAQ,QAAQ;IACvC;QACE,KAAK,CAAC,QAAQ,CAAC,CAAC;IAClB,CAAC;CACF;AAJD,kCAIC"}